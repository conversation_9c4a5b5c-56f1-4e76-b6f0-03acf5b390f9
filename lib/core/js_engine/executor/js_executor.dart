import 'dart:async';
import 'dart:convert';

import 'package:asset_force_mobile_v2/core/env/env_helper.dart';
import 'package:asset_force_mobile_v2/core/js_engine/js_engine.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_asset_item_wrapper.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_view_controller.dart';
import 'package:flutter/services.dart' show rootBundle;
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

/// JsCode执行代码种类
///
/// [customizedLogicJavascript] 自定义逻辑 JavaScript
/// [calculateJavascript] 计算用 JavaScript
/// [saveJavaScript] 保存用 JavaScript
/// [noLocal] 不依赖本地预设代码
enum JsCodeLocal { customizedLogicJavascript, calculateJavascript, saveJavaScript, noLocal }

/// 执行请求信息
class _ExecutionRequest {
  final String javaScriptText;
  final JsCodeLocal codeType;
  final bool needsResult;
  final bool waitForCompletion;
  final Completer<String> resultCompleter;
  final DateTime timestamp;
  final String requestId; // 唯一请求标识符

  _ExecutionRequest({
    required this.javaScriptText,
    required this.codeType,
    required this.needsResult,
    required this.waitForCompletion,
    required this.resultCompleter,
    DateTime? timestamp,
    String? requestId,
  }) : timestamp = timestamp ?? DateTime.now(),
       requestId = requestId ?? '${DateTime.now().millisecondsSinceEpoch}_${codeType.name}';
}

/// JavaScript 执行器
///
class JsExecutor {
  // ==================== 静态属性 ====================

  /// 是否启用 JavaScript 转换的详细日志
  static bool enableJsTransformLogging = false;

  // ==================== 私有属性 ====================

  /// WebView 控制器
  InAppWebViewController? _webViewController;

  /// 是否使用外部提供的 WebViewController
  bool _hasExternalController = false;

  /// 无头 WebView
  ///
  /// 使用无头webview提高运行性能，降低内存占用
  HeadlessInAppWebView? _headlessWebView;

  /// JavaScript 与 Flutter 之间的桥接服务
  late final JsFlutterBridgeService _bridgeService;

  /// 是否已经完成 WebView 初始化
  bool isInitWebView = false;

  /// 是否已经完成基础初始化（HTML加载、监听器设置等）
  bool _isBasicInitialized = false;

  /// 用于等待异步执行完成的Completer - 按请求类型分离
  final Map<JsCodeLocal, Completer<void>?> _executionCompleters = {};

  /// 请求队列 - 按类型管理并发请求
  final Map<JsCodeLocal, List<_ExecutionRequest>> _requestQueues = {};

  /// 当前正在执行的请求 - 按类型管理
  final Map<JsCodeLocal, _ExecutionRequest?> _currentRequests = {};

  /// 当前正在等待回调的请求ID - 按代码类型管理
  final Map<JsCodeLocal, String?> _currentWaitingRequestIds = {};

  /// 请求超时时间（毫秒）
  static const Duration _requestTimeout = Duration(seconds: 30);

  // ==================== 性能优化相关属性 ====================

  /// JavaScript 文件缓存
  /// 避免重复读取文件，提高执行性能
  static final Map<String, String> _jsFileCache = {};

  /// 上次数据字典的哈希值

  /// 上次数据字典的哈希值
  /// 用于判断数据是否真正发生变化，避免不必要的重新注入
  int? _lastDataDictHash;

  /// 批量更新管理器
  /// 用于管理JS执行期间的数据更新，避免频繁的UI刷新
  BatchUpdateManager? _batchUpdateManager;

  // ==================== 构造函数 ====================

  /// 默认构造函数 - 创建内部 WebView
  JsExecutor(JsFlutterBridgeService bridgeService) {
    _bridgeService = bridgeService;
    _setupCodeExecutionCallback();
    _initializeRequestManagement();
  }

  // ==================== Getter 方法 ====================

  /// 获取 WebViewController
  InAppWebViewController get webViewController {
    if (_webViewController == null) {
      throw StateError('WebViewController 尚未初始化，请先调用 initWebView()');
    }
    return _webViewController!;
  }

  // ==================== 私有初始化方法 ====================

  /// 初始化请求管理
  void _initializeRequestManagement() {
    // 初始化所有类型的请求管理结构
    for (final codeType in JsCodeLocal.values) {
      _executionCompleters[codeType] = null;
      _requestQueues[codeType] = [];
      _currentRequests[codeType] = null;
      _currentWaitingRequestIds[codeType] = null;
    }
  }

  /// 设置代码执行完成回调
  ///
  /// 在构造函数中调用，设置桥接服务的回调函数
  void _setupCodeExecutionCallback() {
    _bridgeService.setCodeExecutionSuccessCallback(() {
      _onCodeExecutionSuccess();
    });
  }

  /// 代码执行成功回调处理
  ///
  /// 当 JavaScript 代码执行成功时被调用，用于重置执行状态
  void _onCodeExecutionSuccess() {
    LogUtil.d('JsExecutor: 收到代码执行完成回调');

    // 查找所有等待回调的请求，优先处理计算类型
    _ExecutionRequest? currentRequest;
    JsCodeLocal? foundCodeType;

    // 优先处理计算类型的请求，因为它们通常是并发执行的
    final priorityOrder = [
      JsCodeLocal.calculateJavascript,
      JsCodeLocal.customizedLogicJavascript,
      JsCodeLocal.saveJavaScript,
      JsCodeLocal.noLocal,
    ];

    for (final codeType in priorityOrder) {
      final waitingRequestId = _currentWaitingRequestIds[codeType];
      final request = _currentRequests[codeType];

      if (waitingRequestId != null && request != null && request.requestId == waitingRequestId) {
        currentRequest = request;
        foundCodeType = codeType;
        LogUtil.d('JsExecutor: 找到匹配的请求 - 类型: ${codeType.name}, ID: ${request.requestId}');
        break;
      }
    }

    if (currentRequest != null && foundCodeType != null) {
      LogUtil.d('JsExecutor: 代码执行完成，重置执行状态 - ${currentRequest.codeType.name} (请求ID: ${currentRequest.requestId})');

      // 完成 Completer，通知等待的调用者
      if (_executionCompleters[currentRequest.codeType] != null) {
        LogUtil.d('JsExecutor: 完成 Completer - 类型: ${currentRequest.codeType.name}');
        _executionCompleters[currentRequest.codeType]?.complete();
        _executionCompleters[currentRequest.codeType] = null;
      } else {
        LogUtil.w('JsExecutor: Completer 为空 - 类型: ${currentRequest.codeType.name}');
      }

      // 完成结果Completer
      if (!currentRequest.resultCompleter.isCompleted) {
        currentRequest.resultCompleter.complete('');
      }

      // 清除当前请求和等待的请求ID
      _currentRequests[currentRequest.codeType] = null;
      _currentWaitingRequestIds[currentRequest.codeType] = null;

      // 处理队列中的下一个请求
      _processNextRequest(currentRequest.codeType);
    } else {
      LogUtil.w('JsExecutor: 收到代码执行完成回调，但没有找到对应的当前请求');
      LogUtil.d(
        'JsExecutor: 当前等待的请求ID: ${_currentWaitingRequestIds.entries.where((e) => e.value != null).map((e) => '${e.key}: ${e.value}').join(', ')}',
      );
      LogUtil.d(
        'JsExecutor: 当前请求状态: ${_currentRequests.entries.where((e) => e.value != null).map((e) => '${e.key}: ${e.value?.requestId} (${e.value?.timestamp})').join(', ')}',
      );
      LogUtil.d(
        'JsExecutor: 所有Completer状态: ${_executionCompleters.entries.where((e) => e.value != null).map((e) => '${e.key}: ${e.value?.isCompleted}').join(', ')}',
      );
    }
  }

  // ==================== 控制器管理方法 ====================

  /// 设置关联的 AfCustomizeViewController
  ///
  /// 参数:
  /// * [controller] - 要关联的控制器实例
  ///
  /// 功能说明:
  /// 设置桥接服务的控制器，使 JavaScript 函数能够操作 Flutter 端的数据
  void setController(AfCustomizeViewController controller) {
    _bridgeService.setController(controller);
    _initializeBatchUpdateManager();
    LogUtil.d('JsExecutor: 设置控制器 - ${controller.hashCode}');
  }

  /// 清除关联的控制器
  void clearController() {
    _bridgeService.clearController();
    _disposeBatchUpdateManager();
    LogUtil.d('JsExecutor: 清除控制器');
  }

  /// 检查是否已设置控制器
  bool get hasController => _bridgeService.hasController;

  /// 检查是否有批量更新管理器（即是否正在执行 JavaScript）
  bool get hasBatchManager => _batchUpdateManager != null;

  /// 获取当前的数据字典
  ///
  /// 从 controller.assetDict 获取数据
  Map<String, List<RxAssetItemWrapper>>? get _currentDataDict {
    if (_bridgeService.hasController) {
      return _bridgeService.controller?.assetDict;
    }
    // 如果没有设置 controller，返回空
    LogUtil.w('JsExecutor: 未设置控制器，无法获取数据字典');
    return null;
  }

  // ==================== 私有方法 ====================

  /// 创建内部无头 WebView（仅在需要时创建）
  Future<void> _createHeadlessWebView() async {
    _headlessWebView = HeadlessInAppWebView(
      initialSettings: InAppWebViewSettings(javaScriptEnabled: true, isInspectable: false),
      onWebViewCreated: (InAppWebViewController controller) {
        _webViewController = controller;
      },
    );

    await _headlessWebView!.run();
  }

  // ==================== 公共方法 ====================

  /// 执行 JavaScript 代码
  ///
  /// 参数:
  /// * [javaScriptText] - 要执行的 JavaScript 代码
  /// * [codeType] - 代码类型，决定加载哪个基础 JS 文件
  /// * [needsResult] - 是否需要返回执行结果
  /// * [waitForCompletion] - 是否等待执行完成（对于异步代码）
  ///
  /// 返回:
  /// * [String] - 执行结果（如果 needsResult 为 true）
  Future<String> eval(
    String javaScriptText,
    JsCodeLocal codeType, {
    bool needsResult = false,
    bool waitForCompletion = false,
  }) async {
    if (codeType == JsCodeLocal.saveJavaScript) {
      javaScriptText = '$javaScriptText\nawait run();';
    }

    // 对于计算类型，使用专门的并发处理逻辑
    if (codeType == JsCodeLocal.calculateJavascript && needsResult) {
      return await _executeCalculationJavaScript(javaScriptText);
    }

    return await _executeJavaScript(
      javaScriptText,
      codeType,
      needsResult: needsResult,
      waitForCompletion: waitForCompletion,
    );
  }

  /// 专门处理计算类型JavaScript的执行
  ///
  /// 针对计算类型的特殊优化：
  /// 1. 支持高并发执行
  /// 2. 快速返回结果
  /// 3. 减少队列等待时间
  ///
  /// 参数:
  /// * [javaScriptText] - 计算JavaScript代码
  ///
  /// 返回:
  /// * [String] - 计算结果
  Future<String> _executeCalculationJavaScript(String javaScriptText) async {
    try {
      if (!isInitWebView) {
        await initWebView();
        isInitWebView = true;
      }

      // 获取计算JavaScript文件内容
      final jsCode = await _getCachedJsFile(JsCodeLocal.calculateJavascript);

      // 构建同步执行的JavaScript代码（计算类型通常是同步的）
      final wrappedJavaScript = _buildSyncResultWrapper(jsCode, javaScriptText);

      LogUtil.d('JsExecutor: 执行计算JavaScript - 并发优化模式');

      // 直接执行，不需要复杂的队列管理
      final result = await webViewController.evaluateJavascript(source: wrappedJavaScript);

      final resultString = result is String && result.isNotEmpty ? result : result.toString();
      LogUtil.d(
        'JsExecutor: 计算完成，结果: ${resultString.length > 50 ? resultString.substring(0, 50) + "..." : resultString}',
      );

      return resultString;
    } catch (e) {
      LogUtil.e('JsExecutor: 计算JavaScript执行失败 - $e');
      return '';
    }
  }

  /// 处理队列中的下一个请求
  void _processNextRequest(JsCodeLocal codeType) {
    final queue = _requestQueues[codeType]!;
    if (queue.isNotEmpty) {
      final nextRequest = queue.removeAt(0);
      _executeRequest(nextRequest);
    }
  }

  /// 执行请求
  Future<void> _executeRequest(_ExecutionRequest request) async {
    _currentRequests[request.codeType] = request;

    LogUtil.d(
      'JsExecutor: 开始执行请求 - 类型: ${request.codeType.name}, ID: ${request.requestId}, 等待完成: ${request.waitForCompletion}',
    );

    // 如果需要等待异步执行完成，设置当前等待的请求ID
    if (request.waitForCompletion) {
      _currentWaitingRequestIds[request.codeType] = request.requestId;
      LogUtil.d('JsExecutor: 设置当前等待的请求ID - 类型: ${request.codeType.name}, ID: ${request.requestId}');
    }

    try {
      final result = await _executeJavaScript(
        request.javaScriptText,
        request.codeType,
        needsResult: request.needsResult,
        waitForCompletion: request.waitForCompletion,
      );

      // 完成结果 Completer
      if (!request.resultCompleter.isCompleted) {
        request.resultCompleter.complete(result);
      }
    } catch (e) {
      // 异常情况下完成结果 Completer
      if (!request.resultCompleter.isCompleted) {
        request.resultCompleter.completeError(e);
      }
    } finally {
      // 无论成功还是失败，都清除等待的请求ID
      if (request.waitForCompletion) {
        _currentWaitingRequestIds[request.codeType] = null;
        LogUtil.d('JsExecutor: 清除等待的请求ID - 类型: ${request.codeType.name}');
      }
    }
  }

  /// 实际执行 JavaScript 代码的方法
  ///
  /// 包含性能优化：文件缓存、并发控制、克隆数据等
  Future<String> _executeJavaScript(
    String javaScriptText,
    JsCodeLocal codeType, {
    bool needsResult = false,
    bool waitForCompletion = false,
  }) async {
    _ExecutionRequest? currentRequest;

    try {
      // 检查是否已有同类型的请求正在执行
      if (_currentRequests[codeType] != null) {
        LogUtil.d('JsExecutor: [${codeType.name}] 已有请求正在执行中，加入队列');

        // 创建请求并加入队列
        final request = _ExecutionRequest(
          javaScriptText: javaScriptText,
          codeType: codeType,
          needsResult: needsResult,
          waitForCompletion: waitForCompletion,
          resultCompleter: Completer<String>(),
        );

        _requestQueues[codeType]!.add(request);
        LogUtil.d('JsExecutor: 请求已加入队列 - 类型: ${codeType.name}, ID: ${request.requestId}, 等待完成: ${waitForCompletion}');

        // 等待结果（带超时保护）
        return await request.resultCompleter.future.timeout(_requestTimeout);
      }

      if (!isInitWebView) {
        await initWebView();
        isInitWebView = true;
      }

      // 使用缓存获取 JavaScript 文件内容
      String jsCode = '';
      if (codeType != JsCodeLocal.noLocal) {
        jsCode = await _getCachedJsFile(codeType);
      }

      // 对于自定义逻辑类型的代码，自动为预定义异步函数添加缺少的 await
      String processedJavaScriptText = javaScriptText;
      if (codeType == JsCodeLocal.customizedLogicJavascript) {
        // 设置转换器的日志开关
        JsAsyncTransformer.enableVerboseLogging = enableJsTransformLogging;

        processedJavaScriptText = JsAsyncTransformer.transformToAsync(javaScriptText);
        if (processedJavaScriptText != javaScriptText) {
          LogUtil.d('JsExecutor: 已为预定义异步函数添加缺少的 await');
          if (enableJsTransformLogging) {
            LogUtil.d('JsExecutor: 转换前代码:\n$javaScriptText');
            LogUtil.d('JsExecutor: 转换后代码:\n$processedJavaScriptText');
          }
        }
      }

      // 简化日志输出
      LogUtil.d('JsExecutor: 执行 JavaScript [${codeType.name}] ${needsResult ? "(需要返回值)" : ""}');

      // 根据代码类型决定是否使用异步包装
      final bool useAsyncWrapper =
          codeType == JsCodeLocal.customizedLogicJavascript || codeType == JsCodeLocal.saveJavaScript;

      // 如果需要等待异步执行完成，创建Completer
      if (waitForCompletion && useAsyncWrapper) {
        _executionCompleters[codeType] = Completer<void>();
        LogUtil.d('JsExecutor: 创建 Completer - 类型: ${codeType.name}');
      }

      // 构建 JavaScript 包装代码
      final String wrappedJavaScript = _buildWrappedJavaScript(
        jsCode: jsCode,
        javaScriptText: processedJavaScriptText,
        useAsyncWrapper: useAsyncWrapper,
        needsResult: needsResult,
      );

      // 在执行JavaScript之前，设置当前请求
      currentRequest = _ExecutionRequest(
        javaScriptText: javaScriptText,
        codeType: codeType,
        needsResult: needsResult,
        waitForCompletion: waitForCompletion,
        resultCompleter: Completer<String>(),
      );
      _currentRequests[codeType] = currentRequest;

      // 如果需要等待异步执行完成，设置当前等待的请求ID
      if (waitForCompletion && useAsyncWrapper) {
        _currentWaitingRequestIds[codeType] = currentRequest.requestId;
        LogUtil.d('JsExecutor: 设置当前等待的请求ID - 类型: ${codeType.name}, ID: ${currentRequest.requestId}');
      }

      // 执行 JavaScript 代码
      final result = await webViewController.evaluateJavascript(source: wrappedJavaScript);

      if (needsResult) {
        final resultString = result is String && result.isNotEmpty ? result : result.toString();
        LogUtil.d(
          'JsExecutor: 执行完成，返回值: ${resultString.length > 100 ? resultString.substring(0, 100) + "..." : resultString}',
        );
        // 对于需要返回结果的同步执行，立即重置执行状态
        // 清除当前请求
        _currentRequests[codeType] = null;

        // 处理队列中的下一个请求
        _processNextRequest(codeType);

        // 完成结果Completer
        if (!currentRequest.resultCompleter.isCompleted) {
          currentRequest.resultCompleter.complete(resultString);
        }

        return resultString;
      } else {
        LogUtil.d('JsExecutor: 执行完成');

        // 如果需要等待异步执行完成，并且使用异步包装
        if (waitForCompletion && useAsyncWrapper) {
          // 等待 code_execution_success 事件
          final waitingRequestId = _currentWaitingRequestIds[codeType];
          LogUtil.d('JsExecutor: 等待异步执行完成 - 等待请求ID: $waitingRequestId');
          await _executionCompleters[codeType]?.future.timeout(_requestTimeout);
          LogUtil.d('JsExecutor: 异步执行完成 - 请求ID: $waitingRequestId');
        }

        // 对于不使用异步包装的同步执行，立即重置执行状态
        // 对于使用异步包装的执行，状态将由回调函数重置
        if (!useAsyncWrapper) {
          // 清除当前请求
          _currentRequests[codeType] = null;

          // 处理队列中的下一个请求
          _processNextRequest(codeType);

          // 完成结果Completer
          if (!currentRequest.resultCompleter.isCompleted) {
            currentRequest.resultCompleter.complete('');
          }
        }
        return '';
      }
    } catch (e) {
      LogUtil.e('JsExecutor: 执行失败 - $e');
      // 异常情况下重置执行状态

      // 异常情况下完成Completer
      _executionCompleters[codeType]?.completeError(e);
      _executionCompleters[codeType] = null;

      // 完成结果Completer
      if (currentRequest != null && !currentRequest.resultCompleter.isCompleted) {
        currentRequest.resultCompleter.completeError(e);
      }

      // 清除当前请求和等待的请求ID
      _currentRequests[codeType] = null;
      _currentWaitingRequestIds[codeType] = null;

      // 处理队列中的下一个请求
      _processNextRequest(codeType);

      return '';
    }
  }

  /// 构建包装后的 JavaScript 代码
  ///
  /// 根据不同的执行需求构建相应的 JavaScript 包装代码
  ///
  /// 参数:
  /// * [jsCode] - 基础 JavaScript 代码（预设函数库）
  /// * [javaScriptText] - 用户 JavaScript 代码
  /// * [useAsyncWrapper] - 是否使用异步包装
  /// * [needsResult] - 是否需要返回结果
  ///
  /// 返回:
  /// * [String] - 包装后的完整 JavaScript 代码
  String _buildWrappedJavaScript({
    required String jsCode,
    required String javaScriptText,
    required bool useAsyncWrapper,
    required bool needsResult,
  }) {
    if (needsResult) {
      // 同步执行并返回结果
      // 因为同步执行，所以不需要使用异步包装
      return _buildSyncResultWrapper(jsCode, javaScriptText);
    } else {
      // 异步执行不返回结果
      return useAsyncWrapper
          ? _buildAsyncExecutionWrapper(jsCode, javaScriptText)
          : _buildSyncExecutionWrapper(jsCode, javaScriptText);
    }
  }

  /// 构建同步执行并返回结果的包装代码
  String _buildSyncResultWrapper(String jsCode, String javaScriptText) {
    return '''
      $jsCode
      
      // 执行计算JavaScript代码并获取结果（同步版本）
      (function() {
        try {
          var result = $javaScriptText;
      
          ${_getResultSerializationCode()}
        } catch (error) {
          console.error("JS执行错误:", error);
          return "execution_error: " + error.message;
        }
      })();
    ''';
  }

  /// 构建异步执行不返回结果的包装代码
  ///
  /// [jsCode] - 基础 JavaScript 代码（预设函数）
  /// [javaScriptText] - 用户 JavaScript 代码
  String _buildAsyncExecutionWrapper(String jsCode, String javaScriptText) {
    return '''
      $jsCode
      
      (()=> {
        // 异步执行用户JavaScript代码
        (async function() {
          try {
            if (typeof callFlutterFunction !== 'undefined') {
              await callFlutterFunction('code_execution_start', []);
            }
  
            await (async function() {
              $javaScriptText
            })();
            
            if (typeof callFlutterFunction !== 'undefined') {
              await callFlutterFunction('code_execution_success', []);
            }
          } catch (error) {
            console.error("JS执行错误:", error);
            // 即使出错也要发送成功回调，避免Flutter端无限等待
            if (typeof callFlutterFunction !== 'undefined') {
              await callFlutterFunction('code_execution_success', []);
            }
          }
        })();
        
        // 此处异步返回空避免Js获取到不支持的返回值的问题
        return '';
      })();
    ''';
  }

  /// 构建同步执行不返回结果的包装代码
  String _buildSyncExecutionWrapper(String jsCode, String javaScriptText) {
    return '''
      $jsCode
      $javaScriptText
    ''';
  }

  /// 获取结果序列化的通用代码
  String _getResultSerializationCode() {
    return '''
      // 确保返回值能被正确序列化
      if (result === undefined || result === null) {
        return "execution_completed";
      }
      
      // 如果是Date对象，转换为ISO字符串
      if (result instanceof Date) {
        return result.toISOString();
      }
      
      // 如果是对象，转换为JSON字符串
      if (typeof result === 'object') {
        return JSON.stringify(result);
      }
      
      // 其他类型直接返回
      return result.toString();
    ''';
  }

  /// 获取缓存的 JavaScript 文件内容
  ///
  /// 避免重复读取文件，提高性能
  Future<String> _getCachedJsFile(JsCodeLocal codeType) async {
    final String filePath = codeType == JsCodeLocal.calculateJavascript
        ? 'assets/jscode/calculateJavascript.js'
        : 'assets/jscode/customizedLogicJavascript.js';

    // 检查缓存
    if (_jsFileCache.containsKey(filePath)) {
      return _jsFileCache[filePath]!;
    }

    // 读取文件并缓存
    try {
      final String content = await rootBundle.loadString(filePath);
      _jsFileCache[filePath] = content;
      LogUtil.d('JsExecutor: 缓存 JavaScript 文件 - $filePath');
      return content;
    } catch (e) {
      LogUtil.e('JsExecutor: 读取 JavaScript 文件失败 - $filePath: $e');
      return '';
    }
  }

  /// 初始化 WebView
  ///
  /// 执行完整的 WebView 初始化流程，包括基础初始化和数据注入
  Future<void> initWebView() async {
    // 执行基础初始化
    await _initBasicWebView();

    // 注入数据
    await _injectInitialData();

    // 标记完全初始化完成
    isInitWebView = true;
    LogUtil.i('JsExecutor: WebView 初始化完成');
  }

  /// 刷新 JavaScript 环境中的数据
  ///
  /// 当控制器中的数据发生变化时，调用此方法同步到 JavaScript 环境
  ///
  /// 使用场景:
  /// - 数据异步加载完成后更新
  /// - 数据发生变化需要同步到 JavaScript 环境
  /// - 页面状态变更需要更新上下文数据
  ///
  /// 性能优化:
  /// - 通过哈希值比较避免不必要的重新注入
  /// - 只在数据真正发生变化时才执行注入操作
  ///
  /// 注意：此方法会自动从已设置的控制器中获取最新数据
  Future<void> refreshJavaScriptData() async {
    if (!_bridgeService.hasController) {
      LogUtil.w('JsExecutor: 未设置控制器，无法刷新 JavaScript 数据');
      return;
    }

    // 如果数据发生变化，需要重新初始化批量更新管理器
    if (_batchUpdateManager != null) {
      _disposeBatchUpdateManager();
    }
    _initializeBatchUpdateManager();

    await _reinjectItemDataDictOptimized();
  }

  /// 优化的数据重新注入方法
  ///
  /// 通过哈希值比较避免不必要的重新注入操作
  Future<void> _reinjectItemDataDictOptimized() async {
    // 确保基础初始化已完成
    if (!_isBasicInitialized) {
      LogUtil.w('JsExecutor: WebView 未初始化，无法重新注入数据');
      return;
    }

    try {
      // 预处理数据，确保可以被 JSON 序列化
      final currentData = _currentDataDict ?? {};
      final processedData = preprocessDataForSerialization(currentData);

      // 计算数据哈希值
      final dataHash = processedData.hashCode;

      // 如果数据没有变化，跳过重新注入
      if (_lastDataDictHash == dataHash) {
        LogUtil.d('JsExecutor: 数据未变化，跳过重新注入');
        return;
      }

      // 更新哈希值
      _lastDataDictHash = dataHash;

      // 序列化数据
      final itemDataDictJson = jsonEncode(processedData);

      // 重新注入数据到 JavaScript 环境
      await webViewController.evaluateJavascript(
        source:
            '''
          // 更新 window.pageThis 中的数据
          if (typeof window.pageThis === 'undefined') {
            window.pageThis = {};
          }
  
          window.pageThis.itemDataDict = $itemDataDictJson;
  
          // 更新已存在的变量，避免重复声明
          if (typeof instance !== 'undefined') {
            instance = window.pageThis.itemDataDict;
          } else {
            var instance = window.pageThis.itemDataDict;
          }
          console.log('itemDataDict updated:', instance);
  
          if (typeof that !== 'undefined') {
            that = window.pageThis;
          } else {
            var that = window.pageThis;
          }
  
          // 如果存在 syncInstanceToFlutter 函数，调用它来同步数据
          if (typeof syncInstanceToFlutter === 'function') {
            // syncInstanceToFlutter();
          }
        ''',
      );

      LogUtil.d('JsExecutor: 数据重新注入完成');
    } catch (e) {
      LogUtil.e('JsExecutor: 重新注入数据失败 - $e');
    }
  }

  /// 预处理数据以确保可以被 JSON 序列化
  ///
  /// 参数:
  /// * [data] - 要预处理的数据
  ///
  /// 返回:
  /// * [dynamic] - 处理后可以被 JSON 序列化的数据
  ///
  /// 说明:
  /// 1. 处理 RxAssetItemWrapper 对象，调用其 toJson 方法
  /// 2. 处理嵌套的 Map 和 List 结构
  /// 3. 处理其他复杂对象，尝试调用 toJson 或转换为字符串
  /// 4. 保持基础类型不变
  ///
  /// 注意: 此方法现在是公共的，用于调试和测试
  dynamic preprocessDataForSerialization(dynamic data) {
    if (data == null) return null;

    // 基础类型直接返回
    if (data is String || data is num || data is bool) {
      return data;
    }

    // 处理 List（必须在 RxAssetItemWrapper 检查之前，因为可能是 List<RxAssetItemWrapper>）
    if (data is List) {
      final List<dynamic> result = [];
      for (int i = 0; i < data.length; i++) {
        final item = data[i];
        final processedItem = preprocessDataForSerialization(item);
        result.add(processedItem);
      }
      return result;
    }

    // 处理 Map（必须在 RxAssetItemWrapper 检查之前，因为可能是 Map<String, List<RxAssetItemWrapper>>）
    if (data is Map) {
      final Map<String, dynamic> result = {};
      for (final entry in data.entries) {
        final key = entry.key.toString();
        final value = entry.value;
        final processedValue = preprocessDataForSerialization(value);
        result[key] = processedValue;
      }
      return result;
    }

    // 处理 RxAssetItemWrapper 对象
    if (data.runtimeType.toString().contains('RxAssetItemWrapper')) {
      try {
        final jsonResult = (data as dynamic).toJson();
        return jsonResult;
      } catch (e) {
        LogUtil.e('JsExecutor: 序列化 RxAssetItemWrapper 失败 - $e');
        return {
          'error': 'Failed to serialize RxAssetItemWrapper',
          'type': data.runtimeType.toString(),
          'errorMessage': e.toString(),
        };
      }
    }

    // 处理其他对象
    try {
      // 如果已经是 Map<String, dynamic>，直接返回
      if (data is Map<String, dynamic>) {
        return data;
      }

      // 尝试动态调用 toJson
      final result = (data as dynamic).toJson();
      return result;
    } catch (e) {
      // 如果无法序列化，记录警告并返回字符串表示
      LogUtil.w('JsExecutor: 无法序列化对象 ${data.runtimeType} - $e');
      return {'type': data.runtimeType.toString(), 'value': data.toString(), 'serialization_error': e.toString()};
    }
  }

  /// 基础 WebView 初始化
  ///
  /// 包括：HTML 加载、JavaScript 模式设置、监听器设置等
  /// 这部分只需要执行一次
  Future<void> _initBasicWebView() async {
    if (_isBasicInitialized) {
      return;
    }

    LogUtil.d('JsExecutor: 开始初始化 WebView');

    // 如果没有外部控制器，需要创建内部无头 WebView
    if (!_hasExternalController && _webViewController == null) {
      await _createHeadlessWebView();

      // 等待 WebViewController 准备就绪
      // int attempts = 0;
      // while (_webViewController == null && attempts < 50) {
      //   await Future.delayed(const Duration(milliseconds: 100));
      //   attempts++;
      // }
      if (_webViewController == null) {
        throw Exception('JsExecutor: WebViewController 初始化失败');
      }
    }

    // 添加 JavaScript 处理器
    webViewController.addJavaScriptHandler(
      handlerName: 'FlutterChannel',
      callback: (args) {
        if (args.isNotEmpty) {
          _flutterReceive(args[0].toString());
        }
      },
    );
    webViewController.addJavaScriptHandler(
      handlerName: 'FlutterFunctionChannel',
      callback: (args) async {
        if (args.isNotEmpty) {
          return await _handleFlutterFunctionCall(args[0].toString());
        }
        return null;
      },
    );

    // 加载 HTML 页面（在设置完所有处理器之后）
    await webViewController.loadData(
      data: '''
        <!DOCTYPE html>
        <html>
        <head>
          <title>Console Log Test</title>
        </head>
        <body>
        </body>
        </html>
      ''',
      mimeType: 'text/html',
      encoding: 'utf-8',
    );

    // 等待页面加载完成
    await Future.delayed(const Duration(milliseconds: 500));

    // 标记基础初始化完成
    _isBasicInitialized = true;
  }

  /// 注入初始数据到 WebView
  ///
  /// 包括：存储值、初始化脚本、itemDataDict 等
  /// 这部分可以在数据更新时重新执行
  Future<void> _injectInitialData() async {
    // 准备存储值
    final Map<String, dynamic> storageValues = {
      'tokenLocal': StorageUtils.get<String>(StorageUtils.keyToken),
      'userIdLocal': StorageUtils.get<int>(StorageUtils.keyUserId),
      'tenantIdLocal': StorageUtils.get<String>(StorageUtils.keyTenantId),
      'locationValue': StorageUtils.get<String>(StorageUtils.keyLocation),
      'userName': StorageUtils.get<String>(StorageUtils.keyUserName),
      'lastName': StorageUtils.get<String>(StorageUtils.keyLastName),
      'firstName': StorageUtils.get<String>(StorageUtils.keyFirstName),
      'apiGWHost': EnvHelper.getApiGatewayHost(),
      'apiAuthHost': EnvHelper.getAuthApiHost(),
      'apiHost': EnvHelper.getApiHost(),
      'userRoleListLocal': StorageUtils.get<String>(StorageUtils.keyUserRoleList)?.replaceAll('"', '\"'),
    };

    // 加载初始化脚本
    final String initJavascript = await rootBundle.loadString('assets/jscode/initJavascript.js');

    // 插入存储值和初始化代码
    final String injectedValues = storageValues.entries
        .map((entry) {
          final value = entry.value;
          if (value is String) {
            return 'let ${entry.key} = "${value.replaceAll('"', '\\\"')}";';
          }
          return 'let ${entry.key} = "${value}";';
        })
        .join('\n');

    // 预处理并序列化数据字典
    final currentData = _currentDataDict ?? {};
    final processedData = preprocessDataForSerialization(currentData);
    final itemDataDictJson = jsonEncode(processedData);

    // 执行 JavaScript 初始化代码
    await webViewController.evaluateJavascript(
      source:
          '''
      window.pageThis = {
        itemDataDict: $itemDataDictJson
      };
      var instance = window.pageThis.itemDataDict;
      var that = window.pageThis;

      $initJavascript
      $injectedValues
    ''',
    );
  }

  /// 处理 Flutter 接收的消息
  dynamic _flutterReceive(String resultString) {
    final Map<String, dynamic> request = jsonDecode(resultString);
    final String method = request['method'];
    final dynamic data = request['params'];
    switch (method) {
      case 'userRoleList':
        StorageUtils.set(StorageUtils.keyUserRoleList, data);
      default:
        return;
    }
  }

  /// 处理 Flutter 函数调用
  ///
  /// 参数:
  /// * [jsonString] - 包含函数名和参数的 JSON 字符串
  ///
  /// 格式:
  /// ```json
  /// {
  ///   "function": "functionName",
  ///   "params": [param1, param2, ...]
  /// }
  /// ```
  Future<dynamic> _handleFlutterFunctionCall(String jsonString) async {
    try {
      final Map<String, dynamic> request = jsonDecode(jsonString);
      final String functionName = request['function'];
      final List<dynamic> params = request['params'] ?? [];

      // 调用桥接服务执行函数
      final result = await _bridgeService.callFunction(functionName, params);

      LogUtil.d('JsExecutor: Flutter 函数 $functionName 参数：$params 调用结果: $result');

      // 返回结果给 JavaScript
      return result;
    } catch (e) {
      LogUtil.e('处理 Flutter 函数调用失败: $e');
      return {'error': e.toString()};
    }
  }

  /// 清理资源
  ///
  /// 在不再需要 JsExecutor 时调用，清理相关资源
  void dispose() {
    _lastDataDictHash = null;

    // 清理所有执行中的请求和队列
    for (final codeType in JsCodeLocal.values) {
      // 完成所有等待中的Completer
      _executionCompleters[codeType]?.complete();
      _executionCompleters[codeType] = null;

      // 清理队列中的请求
      for (final request in _requestQueues[codeType]!) {
        if (!request.resultCompleter.isCompleted) {
          request.resultCompleter.completeError(Exception('JsExecutor disposed'));
        }
      }
      _requestQueues[codeType]!.clear();

      // 清除当前请求和等待的请求ID
      _currentRequests[codeType] = null;
      _currentWaitingRequestIds[codeType] = null;
    }

    // 清理批量更新管理器
    _disposeBatchUpdateManager();

    // 清理桥接服务（避免在测试环境中的日志问题）
    _bridgeService.clearController();

    LogUtil.d('JsExecutor: 资源清理完成');
  }

  // ==================== 批量更新模式控制方法 ====================

  /// 初始化批量更新管理器
  ///
  /// 在设置控制器时创建长期存在的批量更新管理器，
  /// 避免每次 JavaScript 执行时都重新创建和销毁
  void _initializeBatchUpdateManager() {
    if (_batchUpdateManager != null) {
      LogUtil.d('JsExecutor: 批量更新管理器已存在，跳过初始化');
      return;
    }

    if (_bridgeService.hasController) {
      // 获取控制器的 assetDict
      final controller = _bridgeService.controller;
      if (controller != null) {
        // 即使 assetDict 为空也要创建批量更新管理器，因为 JavaScript 执行过程中可能会有数据更新
        final cowDataManager = CowDataManager(controller.assetDict);
        _batchUpdateManager = BatchUpdateManager(cowDataManager);

        // 设置批量更新管理器到桥接服务（只设置一次）
        _bridgeService.setBatchUpdateManager(_batchUpdateManager);

        LogUtil.d('JsExecutor: 批量更新管理器初始化完成 (assetDict 项目数: ${controller.assetDict.length})');
      } else {
        LogUtil.w('JsExecutor: 控制器为空，无法初始化批量更新管理器');
      }
    } else {
      LogUtil.w('JsExecutor: 未设置控制器，无法初始化批量更新管理器');
    }
  }

  /// 销毁批量更新管理器
  ///
  /// 在清除控制器或销毁 JsExecutor 时调用
  void _disposeBatchUpdateManager() {
    if (_batchUpdateManager != null) {
      // 清除桥接服务中的批量更新管理器
      _bridgeService.clearBatchUpdateManager();

      // 销毁批量更新管理器
      _batchUpdateManager!.dispose();
      _batchUpdateManager = null;

      LogUtil.d('JsExecutor: 批量更新管理器已销毁');
    }
  }
}
