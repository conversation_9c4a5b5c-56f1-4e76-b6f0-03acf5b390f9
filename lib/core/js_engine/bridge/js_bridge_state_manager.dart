import 'package:asset_force_mobile_v2/core/js_engine/js_engine.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:get/get.dart';

/// JavaScript 桥接状态管理器
///
/// 负责管理克隆数据模式、批量更新等状态
class JsBridgeStateManager extends GetxService {
  /// 是否处于克隆数据模式
  bool _isClonedDataMode = false;

  /// 克隆的数据字典，用于 JavaScript 执行期间的读写操作
  dynamic _clonedItemDataDict;

  /// 批量更新管理器
  /// 由当前执行的 JsExecutor 设置，实现批量更新功能
  BatchUpdateManager? _batchUpdateManager;

  /// 代码执行完成回调函数
  /// 由 JsExecutor 设置，用于在代码执行成功时通知执行器
  void Function()? _onCodeExecutionSuccess;

  /// 设置批量更新管理器
  /// 由当前执行的 JsExecutor 调用，设置其批量更新管理器
  void setBatchUpdateManager(BatchUpdateManager? manager) {
    _batchUpdateManager = manager;
    LogUtil.d('JsBridgeStateManager: 设置批量更新管理器 - ${manager != null ? "已设置" : "已清除"}');
  }

  /// 清除批量更新管理器
  void clearBatchUpdateManager() {
    _batchUpdateManager = null;
    LogUtil.d('JsBridgeStateManager: 清除批量更新管理器');
  }

  /// 检查是否有批量更新管理器
  bool get hasBatchUpdateManager => _batchUpdateManager != null;

  /// 设置代码执行完成回调函数
  /// 由 JsExecutor 调用，用于在代码执行成功时通知执行器
  void setCodeExecutionSuccessCallback(void Function()? callback) {
    _onCodeExecutionSuccess = callback;
    LogUtil.d('JsBridgeStateManager: 设置代码执行完成回调 - ${callback != null ? "已设置" : "已清除"}');
  }

  /// 设置克隆数据模式
  ///
  /// 参数:
  /// * [enabled] - 是否启用克隆数据模式
  /// * [clonedData] - 克隆的数据字典（启用时传入，禁用时传入 null）
  ///
  /// 功能说明:
  /// 当启用克隆数据模式时，setValue 和 getValue 等操作都在克隆数据上进行，
  /// 不会影响真实数据。当禁用时，需要手动同步变化到真实数据。
  void setClonedDataMode(bool enabled, dynamic clonedData) {
    _isClonedDataMode = enabled;
    _clonedItemDataDict = clonedData;
    LogUtil.d('JsBridgeStateManager: 克隆数据模式 ${enabled ? "启用" : "禁用"}');
  }

  /// 检查是否处于克隆数据模式
  bool get isClonedDataMode => _isClonedDataMode;

  /// 获取克隆的数据字典
  dynamic get clonedItemDataDict => _clonedItemDataDict;

  /// 获取批量更新管理器
  BatchUpdateManager? get batchUpdateManager => _batchUpdateManager;

  /// 获取代码执行成功回调函数
  void Function()? get onCodeExecutionSuccess => _onCodeExecutionSuccess;

  /// 代码执行成功
  Future<void> handleCodeExecutionSuccess() async {
    LogUtil.d('JsBridgeStateManager: 代码执行成功 - 开始处理');
    LogUtil.d('JsBridgeStateManager: 批量更新管理器状态: ${_batchUpdateManager != null ? "存在" : "不存在"}');
    LogUtil.d('JsBridgeStateManager: 代码执行完成回调状态: ${_onCodeExecutionSuccess != null ? "已设置" : "未设置"}');

    // 只有在批量模式中才提交批量更新
    if (_batchUpdateManager != null) {
      // 检查是否在批量模式中
      if (_batchUpdateManager!.isBatchMode) {
        await _batchUpdateManager!.commitBatch();
        LogUtil.d('JsBridgeStateManager: 批量更新提交完成');
      } else {
        LogUtil.d('JsBridgeStateManager: 不在批量模式中，跳过批量更新提交');
      }
    }

    // 调用回调函数通知 JsExecutor 代码执行完成
    LogUtil.d('JsBridgeStateManager: 调用代码执行完成回调');
    _onCodeExecutionSuccess?.call();
    LogUtil.d('JsBridgeStateManager: 代码执行成功处理完成');
  }

  /// 代码执行开始
  void handleCodeExecutionStart() {
    LogUtil.d('JsBridgeStateManager: 代码执行开始');
    _batchUpdateManager?.beginBatch();
  }
}
