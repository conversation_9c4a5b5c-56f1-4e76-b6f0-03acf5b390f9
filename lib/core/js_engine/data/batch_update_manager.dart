import 'dart:async';

import 'package:asset_force_mobile_v2/core/js_engine/js_engine.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';

/// 批量更新管理器
///
/// 负责管理UI更新的批量处理，避免JS执行期间的频繁UI刷新
/// 核心功能：
/// 1. 延迟UI更新直到JS执行完成
/// 2. 批量处理所有数据变更
/// 3. 提供事务性的更新机制
/// 4. 支持更新优先级和依赖关系
class BatchUpdateManager {
  /// 数据管理器
  final CowDataManager _dataManager;

  /// 是否正在批量更新模式
  bool _isBatchMode = false;

  /// 批量更新完成的回调
  final List<VoidCallback> _batchCompleteCallbacks = [];

  /// 更新任务队列
  final List<_UpdateTask> _updateQueue = [];

  /// 批量更新的超时时间（毫秒）
  static const int _batchTimeoutMs = 5000;

  /// 批量更新的定时器
  Timer? _batchTimer;

  BatchUpdateManager(this._dataManager);

  /// 检查是否在批量模式中
  bool get isBatchMode => _isBatchMode;

  /// 开始批量更新模式
  ///
  /// 在此模式下，所有的数据更新都会被延迟到批量提交时执行
  void beginBatch() {
    if (_isBatchMode) {
      LogUtil.w('BatchUpdateManager: 已经在批量模式中');
      return;
    }

    _isBatchMode = true;
    _dataManager.beginTransaction();

    // 设置超时保护，防止批量更新永远不结束
    _batchTimer = Timer(const Duration(milliseconds: _batchTimeoutMs), () {
      LogUtil.w('BatchUpdateManager: 批量更新超时，强制提交');
      commitBatch();
    });

    LogUtil.d('BatchUpdateManager: 开始批量更新模式');
  }

  /// 提交批量更新
  ///
  /// 将所有累积的更新应用到原始数据，并触发UI刷新
  Future<void> commitBatch() async {
    if (!_isBatchMode) {
      LogUtil.w('BatchUpdateManager: 不在批量模式中，无法提交');
      return;
    }

    try {
      _cancelBatchTimer();

      // 执行队列中的更新任务
      await _executeUpdateQueue();

      // 提交数据管理器的事务
      await _dataManager.commitTransaction();

      // 执行批量完成回调
      _executeBatchCompleteCallbacks();

      LogUtil.d('BatchUpdateManager: 批量更新提交成功');
    } catch (e) {
      LogUtil.e('BatchUpdateManager: 批量更新提交失败 - $e');
      // 发生错误时回滚
      await rollbackBatch();
      rethrow;
    } finally {
      _isBatchMode = false;
      _clearUpdateQueue();
    }
  }

  /// 回滚批量更新
  ///
  /// 撤销所有未提交的更新
  Future<void> rollbackBatch() async {
    if (!_isBatchMode) {
      LogUtil.w('BatchUpdateManager: 不在批量模式中，无法回滚');
      return;
    }

    try {
      _cancelBatchTimer();
      _dataManager.rollbackTransaction();
      _clearUpdateQueue();

      LogUtil.d('BatchUpdateManager: 批量更新已回滚');
    } finally {
      _isBatchMode = false;
    }
  }

  /// 设置项目值
  ///
  /// 在批量模式下，更新会被延迟；否则立即执行
  void setValue(String itemName, dynamic value, {int priority = 0}) {
    if (_isBatchMode) {
      // 批量模式：延迟更新
      _dataManager.setValue(itemName, value);
      _addUpdateTask(_UpdateTask(itemName: itemName, value: value, priority: priority, timestamp: DateTime.now()));
    } else {
      // 非批量模式：立即更新
      _dataManager.beginTransaction();
      _dataManager.setValue(itemName, value);
      _dataManager.commitTransaction();
    }
  }

  /// 获取项目值
  ///
  /// 返回当前的值（可能是修改过的值）
  dynamic getValue(String itemName) {
    return _dataManager.getValue(itemName);
  }

  /// 检查项目是否被修改
  bool isModified(String itemName) {
    return _dataManager.isModified(itemName);
  }

  /// 获取所有修改过的项目
  Set<String> getModifiedItems() {
    return _dataManager.getModifiedItems();
  }

  /// 添加批量完成回调
  ///
  /// 这些回调会在批量更新提交后执行
  void addBatchCompleteCallback(VoidCallback callback) {
    _batchCompleteCallbacks.add(callback);
  }

  /// 移除批量完成回调
  void removeBatchCompleteCallback(VoidCallback callback) {
    _batchCompleteCallbacks.remove(callback);
  }

  /// 获取当前状态信息
  Map<String, dynamic> getStatus() {
    return {
      'isBatchMode': _isBatchMode,
      'updateQueueSize': _updateQueue.length,
      'modificationCount': _dataManager.modificationCount,
      'modifiedItems': _dataManager.getModifiedItems().toList(),
      'hasPendingTimer': _batchTimer?.isActive ?? false,
    };
  }

  /// 添加更新任务到队列
  void _addUpdateTask(_UpdateTask task) {
    // 检查是否已存在相同项目的任务，如果存在则更新
    final existingIndex = _updateQueue.indexWhere((t) => t.itemName == task.itemName);
    if (existingIndex != -1) {
      _updateQueue[existingIndex] = task;
    } else {
      _updateQueue.add(task);
    }

    // 按优先级排序（高优先级在前）
    _updateQueue.sort((a, b) => b.priority.compareTo(a.priority));
  }

  /// 执行更新队列中的任务
  Future<void> _executeUpdateQueue() async {
    if (_updateQueue.isEmpty) return;

    LogUtil.d('BatchUpdateManager: 执行 ${_updateQueue.length} 个更新任务');

    // 按优先级顺序执行任务
    for (final task in _updateQueue) {
      try {
        // 这里可以添加特殊的更新逻辑，比如依赖检查、验证等
        LogUtil.d('BatchUpdateManager: 执行更新任务 - ${task.itemName}');
      } catch (e) {
        LogUtil.e('BatchUpdateManager: 更新任务执行失败 - ${task.itemName}: $e');
      }
    }
  }

  /// 执行批量完成回调
  void _executeBatchCompleteCallbacks() {
    for (final callback in _batchCompleteCallbacks) {
      try {
        callback();
      } catch (e) {
        LogUtil.e('BatchUpdateManager: 批量完成回调执行失败 - $e');
      }
    }
  }

  /// 取消批量定时器
  void _cancelBatchTimer() {
    _batchTimer?.cancel();
    _batchTimer = null;
  }

  /// 清空更新队列
  void _clearUpdateQueue() {
    _updateQueue.clear();
    _batchCompleteCallbacks.clear();
  }

  /// 释放资源
  void dispose() {
    if (_isBatchMode) {
      LogUtil.w('BatchUpdateManager: 销毁时仍在批量模式，强制提交');
      commitBatch();
    }

    _cancelBatchTimer();
    _clearUpdateQueue();
    _dataManager.dispose();

    LogUtil.d('BatchUpdateManager: 资源已释放');
  }
}

/// 更新任务
class _UpdateTask {
  final String itemName;
  final dynamic value;
  final int priority;
  final DateTime timestamp;

  _UpdateTask({required this.itemName, required this.value, required this.priority, required this.timestamp});

  @override
  String toString() {
    return '_UpdateTask(itemName: $itemName, value: $value, priority: $priority, timestamp: $timestamp)';
  }
}

/// 空回调类型定义
typedef VoidCallback = void Function();
